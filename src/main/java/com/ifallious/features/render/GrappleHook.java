package com.ifallious.features.render;

import com.ifallious.event.GlobalEventBus;
import com.ifallious.event.TickEvent;
import com.ifallious.utils.Utils;
import com.ifallious.utils.render.BlockRenderer;
import gg.essential.universal.UMinecraft;
import gg.essential.universal.wrappers.UPlayer;
import meteordevelopment.meteorclient.renderer.ShapeMode;
import meteordevelopment.meteorclient.utils.render.color.Color;
import meteordevelopment.orbit.EventHandler;
import net.minecraft.util.math.BlockPos;

public class GrappleHook {
    BlockRenderer renderer;
    public GrappleHook() {
        renderer = new BlockRenderer();
        GlobalEventBus.subscribe(this);
    }

    @EventHandler
    public void onTick(TickEvent e) {
        renderer.clearBlocks();
        BlockPos target = Utils.getTargetedBlockPos();
        if (target == null) return;
        float distance = Utils.distance3D(target.getX(), target.getY(), target.getZ(), (float) UPlayer.getPosX(), (float) UPlayer.getPosY(), (float) UPlayer.getPosZ());
        renderer.addBlock(target.getX(), target.getY(), target.getZ(), new Color(0, 0, 0, 0), distance < 30? new Color(0, 255, 0, 100) : new Color(255, 0 ,0, 100), ShapeMode.Lines);
    }
}
